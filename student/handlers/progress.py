from aiogram import Router, F
from aiogram.types import CallbackQuery, InputMediaPhoto
from aiogram.fsm.context import FSMContext
from aiogram.fsm.state import StatesGroup, State

from common.utils import check_if_id_in_callback_data
from ..keyboards.progress import get_progress_menu_kb, get_subjects_progress_kb, get_back_to_progress_kb
# Импорты универсальных функций микротем
from common.microtopics.handlers import (
    show_detailed_microtopics_universal,
    show_summary_microtopics_universal
)
from common.microtopics.register_handlers import register_microtopics_pagination_handlers

router = Router()

class ProgressStates(StatesGroup):
    main = State()
    subjects = State()
    common_stats = State()
    subject_details = State()

    # Детальная статистика
    detailed_stats = State()  # Детальная статистика по микротемам
    summary_stats = State()   # Сводка сильных/слабых тем

@router.callback_query(F.data == "progress")
async def show_progress_menu(callback: CallbackQuery, state: FSMContext):
    """Показать меню прогресса"""
    from common.premium_access import check_premium_access, show_premium_required_message, PREMIUM_FEATURES

    # Устанавливаем состояние для корректной навигации
    await state.set_state(ProgressStates.main)

    # Проверяем доступ к премиум функциям
    has_premium = await check_premium_access(callback.from_user.id)

    if not has_premium:
        feature_info = PREMIUM_FEATURES["progress"]
        await show_premium_required_message(
            callback,
            feature_info["name"],
            feature_info["description"]
        )
        return

        # Проверяем, есть ли текст в сообщении для редактирования
    try:
        await callback.message.edit_text(
            "Что именно хочешь посмотреть? Выбери один из вариантов:",
            reply_markup=get_progress_menu_kb()
        )
    except Exception as e:
        # Если не можем редактировать (например, сообщение с фото), удаляем и отправляем новое
        try:
            await callback.message.delete()
        except Exception as delete_error:
            print(f"Не удалось удалить сообщение: {delete_error}")

        await callback.message.answer(
            "Что именно хочешь посмотреть? Выбери один из вариантов:",
            reply_markup=get_progress_menu_kb()
        )

@router.callback_query(ProgressStates.main, F.data == "general_stats")
async def show_general_stats(callback: CallbackQuery, state: FSMContext):
    """Показать общую статистику"""
    from database import StudentRepository

    # Получаем студента по Telegram ID
    student = await StudentRepository.get_by_telegram_id(callback.from_user.id)

    if not student:
        # Проверяем, есть ли текст в сообщении для редактирования
        try:
            await callback.message.edit_text(
                "❌ Студент не найден в системе",
                reply_markup=get_back_to_progress_kb()
            )
        except Exception as e:
            # Если не можем редактировать (например, сообщение с фото), удаляем и отправляем новое
            try:
                await callback.message.delete()
            except Exception as delete_error:
                print(f"Не удалось удалить сообщение: {delete_error}")

            await callback.message.answer(
                "❌ Студент не найден в системе",
                reply_markup=get_back_to_progress_kb()
            )
        return

    # Получаем общую статистику студента
    general_stats = await StudentRepository.get_general_stats(student.id)

    # Проверяем, есть ли текст в сообщении для редактирования
    try:
        await callback.message.edit_text(
            f"Вот твоя краткая статистика 👇\n"
            f"📊 Баллы: {general_stats.get('total_points', 0)}\n"
            f"🎯 Уровень: {student.level}\n"
            f"📋 Выполнено ДЗ: {general_stats.get('unique_completed', 0)} (всего попыток: {general_stats.get('total_completed', 0)})",
            reply_markup=get_back_to_progress_kb()
        )
    except Exception as e:
        # Если не можем редактировать (например, сообщение с фото), удаляем и отправляем новое
        try:
            await callback.message.delete()
        except Exception as delete_error:
            print(f"Не удалось удалить сообщение: {delete_error}")

        await callback.message.answer(
            f"Вот твоя краткая статистика 👇\n"
            f"📊 Баллы: {general_stats.get('total_points', 0)}\n"
            f"🎯 Уровень: {student.level}\n"
            f"📋 Выполнено ДЗ: {general_stats.get('unique_completed', 0)} (всего попыток: {general_stats.get('total_completed', 0)})",
            reply_markup=get_back_to_progress_kb()
        )
    await state.set_state(ProgressStates.common_stats)

@router.callback_query(ProgressStates.main, F.data == "topics_understanding")
async def show_subjects_list(callback: CallbackQuery, state: FSMContext):
    """Показать список предметов для просмотра понимания по темам"""
    # Проверяем, есть ли текст в сообщении для редактирования
    try:
        await callback.message.edit_text(
            "Выбери предмет, чтобы посмотреть % понимания по микротемам:",
            reply_markup=await get_subjects_progress_kb(user_id=callback.from_user.id)
        )
    except Exception as e:
        # Если не можем редактировать (например, сообщение с фото), удаляем и отправляем новое
        try:
            await callback.message.delete()
        except Exception as delete_error:
            print(f"Не удалось удалить сообщение: {delete_error}")

        await callback.message.answer(
            "Выбери предмет, чтобы посмотреть % понимания по микротемам:",
            reply_markup=await get_subjects_progress_kb(user_id=callback.from_user.id)
        )
    await state.set_state(ProgressStates.subjects)

@router.callback_query(ProgressStates.subjects, F.data.startswith("progress_sub_"))
async def show_subject_progress(callback: CallbackQuery, state: FSMContext):
    """Показать кнопки выбора типа статистики по предмету"""
    subject_id = await check_if_id_in_callback_data("progress_sub_", callback, state, "subject")

    # Получаем ID студента из Telegram ID
    from database import StudentRepository, SubjectRepository
    student = await StudentRepository.get_by_telegram_id(callback.from_user.id)

    if not student:
        await callback.message.edit_text(
            "❌ Студент не найден в системе",
            reply_markup=get_back_to_progress_kb()
        )
        return

    # Получаем название предмета
    subject = await SubjectRepository.get_by_id(int(subject_id))
    subject_name = subject.name if subject else "Предмет"

    # Показываем кнопки выбора типа статистики
    from common.analytics.keyboards import get_student_microtopics_kb

    await callback.message.edit_text(
        f"📊 Выбери тип статистики по предмету {subject_name}:",
        reply_markup=get_student_microtopics_kb(student.id, int(subject_id))
    )
    await state.set_state(ProgressStates.subject_details)

    # Сохраняем данные для навигации
    await state.update_data(
        progress_student_id=student.id,
        progress_subject_id=int(subject_id)
    )

# Добавляем обработчики для кнопок статистики
@router.callback_query(ProgressStates.subject_details, F.data.startswith("microtopics_detailed_"))
async def show_detailed_microtopics(callback: CallbackQuery, state: FSMContext):
    """Показать детальную статистику по микротемам"""
    from common.premium_access import check_premium_access, show_premium_required_message, PREMIUM_FEATURES

    # Проверяем доступ к премиум функциям для детальной аналитики
    has_premium = await check_premium_access(callback.from_user.id)

    if not has_premium:
        feature_info = PREMIUM_FEATURES["detailed_analytics"]
        await show_premium_required_message(
            callback,
            feature_info["name"],
            feature_info["description"]
        )
        return

    from common.image_utils import generate_microtopics_table_image
    from database import StudentRepository, SubjectRepository, MicrotopicRepository
    from aiogram.types import BufferedInputFile

    # Извлекаем student_id и subject_id из callback_data
    parts = callback.data.split("_")
    if len(parts) >= 4:
        student_id = int(parts[2])
        subject_id = int(parts[3])

        try:
            # Получаем данные студента и предмета
            student = await StudentRepository.get_by_id(student_id)
            subject = await SubjectRepository.get_by_id(subject_id)

            if not student or not subject:
                await callback.message.edit_text(
                    "❌ Студент или предмет не найден",
                    reply_markup=get_back_to_progress_kb()
                )
                return

            # Получаем данные по микротемам
            microtopic_data = await StudentRepository.get_microtopic_understanding(student_id, subject_id)
            microtopics = await MicrotopicRepository.get_by_subject(subject_id)
            microtopic_names = {mt.number: mt.name for mt in microtopics}

            if not microtopic_data:
                await callback.message.edit_text(
                    f"📌 {student.user.name}\n❌ Пока не выполнено ни одного задания по микротемам предмета {subject.name}",
                    reply_markup=get_back_to_progress_kb()
                )
                return

            # Формируем заголовок
            title = f"📌 {student.user.name}\n📈 % понимания по микротемам\n📗 {subject.name}"

            # Генерируем изображение таблицы (первая страница)
            page = 0
            items_per_page = 15
            total_items = len(microtopic_data)

            image_bytes = await generate_microtopics_table_image(
                microtopic_data=microtopic_data,
                microtopic_names=microtopic_names,
                title=title,
                display_mode="detailed",
                data_source="student",
                page=page,
                items_per_page=items_per_page
            )

            # Отправляем изображение
            photo = BufferedInputFile(image_bytes, filename="microtopics_detailed.png")
            await callback.message.delete()

            from ..keyboards.progress import get_microtopics_pagination_kb
            await callback.message.answer_photo(
                photo=photo,
                caption="📊 Детальная статистика по микротемам",
                reply_markup=get_microtopics_pagination_kb(
                    current_page=page,
                    total_items=total_items,
                    items_per_page=items_per_page,
                    callback_prefix="student_microtopics_page"
                )
            )

            # Устанавливаем состояние детальной статистики
            await state.set_state(ProgressStates.detailed_stats)

            # Сохраняем данные для навигации и пагинации
            await state.update_data(
                progress_student_id=student_id,
                progress_subject_id=subject_id,
                microtopic_data=microtopic_data,
                microtopic_names=microtopic_names,
                display_title=title,
                current_page=page,
                items_per_page=items_per_page,
                total_items=total_items
            )

        except Exception as e:
            print(f"Ошибка при генерации изображения микротем: {e}")
            await callback.message.edit_text(
                "❌ Ошибка при загрузке статистики",
                reply_markup=get_back_to_progress_kb()
            )
    else:
        await callback.message.edit_text(
            "❌ Ошибка в данных запроса",
            reply_markup=get_back_to_progress_kb()
        )

@router.callback_query(ProgressStates.subject_details, F.data.startswith("microtopics_summary_"))
async def show_summary_microtopics(callback: CallbackQuery, state: FSMContext):
    """Показать сводку по сильным и слабым темам"""
    from common.premium_access import check_premium_access, show_premium_required_message, PREMIUM_FEATURES

    # Проверяем доступ к премиум функциям для расширенной статистики
    has_premium = await check_premium_access(callback.from_user.id)

    if not has_premium:
        feature_info = PREMIUM_FEATURES["advanced_statistics"]
        await show_premium_required_message(
            callback,
            feature_info["name"],
            feature_info["description"]
        )
        return

    from common.image_utils import generate_microtopics_table_image
    from database import StudentRepository, SubjectRepository, MicrotopicRepository
    from aiogram.types import BufferedInputFile

    # Извлекаем student_id и subject_id из callback_data
    parts = callback.data.split("_")
    if len(parts) >= 4:
        student_id = int(parts[2])
        subject_id = int(parts[3])

        try:
            # Получаем данные студента и предмета
            student = await StudentRepository.get_by_id(student_id)
            subject = await SubjectRepository.get_by_id(subject_id)

            if not student or not subject:
                await callback.message.edit_text(
                    "❌ Студент или предмет не найден",
                    reply_markup=get_back_to_progress_kb()
                )
                return

            # Получаем данные по микротемам
            microtopic_data = await StudentRepository.get_microtopic_understanding(student_id, subject_id)
            microtopics = await MicrotopicRepository.get_by_subject(subject_id)
            microtopic_names = {mt.number: mt.name for mt in microtopics}

            if not microtopic_data:
                await callback.message.edit_text(
                    f"📌 {student.user.name}\n❌ Пока не выполнено ни одного задания для анализа сильных и слабых тем по предмету {subject.name}",
                    reply_markup=get_back_to_progress_kb()
                )
                return

            # Формируем заголовок
            title = f"📌 {student.user.name}\n🔍 Сильные и слабые темы\n📗 {subject.name}"

            # Генерируем изображение таблицы (режим сводки с пагинацией)
            page = 0
            items_per_page = 15  # Меньше элементов для сводки
            total_items = len(microtopic_data)

            image_bytes = await generate_microtopics_table_image(
                microtopic_data=microtopic_data,
                microtopic_names=microtopic_names,
                title=title,
                display_mode="summary",
                data_source="student",
                page=page,
                items_per_page=items_per_page
            )

            # Отправляем изображение
            photo = BufferedInputFile(image_bytes, filename="microtopics_summary.png")
            await callback.message.delete()

            from ..keyboards.progress import get_microtopics_pagination_kb
            await callback.message.answer_photo(
                photo=photo,
                caption="📊 Сводка по сильным и слабым темам",
                reply_markup=get_microtopics_pagination_kb(
                    current_page=page,
                    total_items=total_items,
                    items_per_page=items_per_page,
                    callback_prefix="student_summary_page"
                )
            )

            # Устанавливаем состояние сводки
            await state.set_state(ProgressStates.summary_stats)

            # Сохраняем данные для навигации и пагинации
            await state.update_data(
                progress_student_id=student_id,
                progress_subject_id=subject_id,
                microtopic_data=microtopic_data,
                microtopic_names=microtopic_names,
                display_title=title,
                current_page=page,
                items_per_page=items_per_page,
                total_items=total_items
            )

        except Exception as e:
            print(f"Ошибка при генерации изображения сводки микротем: {e}")
            await callback.message.edit_text(
                "❌ Ошибка при загрузке сводки",
                reply_markup=get_back_to_progress_kb()
            )
    else:
        await callback.message.edit_text(
            "❌ Ошибка в данных запроса",
            reply_markup=get_back_to_progress_kb()
        )

# Специальный обработчик кнопки "Назад" для состояний с картинками микротем
@router.callback_query(F.data == "back_from_microtopics_image")
async def back_from_microtopics_image(callback: CallbackQuery, state: FSMContext):
    """Возврат из состояний с картинками микротем к кнопкам выбора типа"""
    await show_subject_progress_by_saved_data(callback, state)

# Функция для возврата к кнопкам выбора статистики (для навигации)
async def show_subject_progress_by_saved_data(callback, state):
    """Показать кнопки выбора типа статистики по сохраненным данным"""
    try:
        # Получаем сохраненные данные
        data = await state.get_data()
        student_id = data.get('progress_student_id')
        subject_id = data.get('progress_subject_id')

        if not student_id or not subject_id:
            # Если данных нет, возвращаемся к списку предметов
            await show_subjects_list(callback, state)
            return

        # Получаем название предмета
        from database import SubjectRepository
        subject = await SubjectRepository.get_by_id(subject_id)
        subject_name = subject.name if subject else "Предмет"

        # Показываем кнопки выбора типа статистики
        from common.analytics.keyboards import get_student_microtopics_kb

        # Удаляем сообщение с фото и отправляем новое текстовое
        try:
            await callback.message.delete()
        except Exception as delete_error:
            print(f"Не удалось удалить сообщение: {delete_error}")

        await callback.message.answer(
            f"📊 Выбери тип статистики по предмету {subject_name}:",
            reply_markup=get_student_microtopics_kb(student_id, subject_id)
        )
        await state.set_state(ProgressStates.subject_details)

    except Exception as e:
        print(f"Ошибка при возврате к выбору статистики: {e}")
        await show_subjects_list(callback, state)


# Обработчик пагинации для детальной статистики микротем
@router.callback_query(ProgressStates.detailed_stats, F.data.startswith("student_microtopics_page_"))
async def handle_microtopics_pagination(callback: CallbackQuery, state: FSMContext):
    """Обработчик пагинации для детальной статистики микротем"""
    try:
        # Извлекаем номер страницы из callback_data
        page_str = callback.data.split("student_microtopics_page_")[1]
        new_page = int(page_str)

        # Получаем сохраненные данные
        data = await state.get_data()
        microtopic_data = data.get('microtopic_data')
        microtopic_names = data.get('microtopic_names')
        title = data.get('display_title')
        items_per_page = data.get('items_per_page', 30)
        total_items = data.get('total_items')

        if not microtopic_data or not microtopic_names:
            await callback.answer("❌ Ошибка: данные не найдены")
            return

        # Генерируем изображение для новой страницы
        from common.image_utils import generate_microtopics_table_image
        from aiogram.types import BufferedInputFile

        image_bytes = await generate_microtopics_table_image(
            microtopic_data=microtopic_data,
            microtopic_names=microtopic_names,
            title=title,
            display_mode="detailed",
            data_source="student",
            page=new_page,
            items_per_page=items_per_page
        )

        # Обновляем изображение
        photo = BufferedInputFile(image_bytes, filename=f"microtopics_detailed_page_{new_page}.png")

        from ..keyboards.progress import get_microtopics_pagination_kb
        await callback.message.edit_media(
            media=InputMediaPhoto(media=photo),
            reply_markup=get_microtopics_pagination_kb(
                current_page=new_page,
                total_items=total_items,
                items_per_page=items_per_page,
                callback_prefix="student_microtopics_page"
            )
        )

        # Обновляем текущую страницу в состоянии
        await state.update_data(current_page=new_page)

        await callback.answer()

    except Exception as e:
        print(f"Ошибка при обработке пагинации микротем: {e}")
        await callback.answer("❌ Ошибка при переключении страницы")


# Обработчик пагинации для сводки микротем
@router.callback_query(ProgressStates.summary_stats, F.data.startswith("student_summary_page_"))
async def handle_summary_pagination(callback: CallbackQuery, state: FSMContext):
    """Обработчик пагинации для сводки микротем"""
    try:
        # Извлекаем номер страницы из callback_data
        page_str = callback.data.split("student_summary_page_")[1]
        new_page = int(page_str)

        # Получаем сохраненные данные
        data = await state.get_data()
        microtopic_data = data.get('microtopic_data')
        microtopic_names = data.get('microtopic_names')
        title = data.get('display_title')
        items_per_page = data.get('items_per_page', 15)
        total_items = data.get('total_items')

        if not microtopic_data or not microtopic_names:
            await callback.answer("❌ Ошибка: данные не найдены")
            return

        # Генерируем изображение для новой страницы
        from common.image_utils import generate_microtopics_table_image
        from aiogram.types import BufferedInputFile

        image_bytes = await generate_microtopics_table_image(
            microtopic_data=microtopic_data,
            microtopic_names=microtopic_names,
            title=title,
            display_mode="summary",
            data_source="student",
            page=new_page,
            items_per_page=items_per_page
        )

        # Обновляем изображение
        photo = BufferedInputFile(image_bytes, filename=f"microtopics_summary_page_{new_page}.png")

        from ..keyboards.progress import get_microtopics_pagination_kb
        await callback.message.edit_media(
            media=InputMediaPhoto(media=photo),
            reply_markup=get_microtopics_pagination_kb(
                current_page=new_page,
                total_items=total_items,
                items_per_page=items_per_page,
                callback_prefix="student_summary_page"
            )
        )

        # Обновляем текущую страницу в состоянии
        await state.update_data(current_page=new_page)

        await callback.answer()

    except Exception as e:
        print(f"Ошибка при обработке пагинации сводки: {e}")
        await callback.answer("❌ Ошибка при переключении страницы")

    except Exception as e:
        print(f"Ошибка при восстановлении экрана статистики: {e}")
        # В случае ошибки возвращаемся к списку предметов
        await show_subjects_list(callback, state)