from aiogram import Router, F
from aiogram.types import CallbackQuery, InputMediaPhoto
from aiogram.fsm.context import FSMContext
from aiogram.fsm.state import StatesGroup, State

from common.utils import check_if_id_in_callback_data
from ..keyboards.progress import get_progress_menu_kb, get_subjects_progress_kb, get_back_to_progress_kb
# Импорт универсальной регистрации обработчиков микротем
from common.microtopics.register_handlers import register_microtopics_handlers

router = Router()

class ProgressStates(StatesGroup):
    main = State()
    subjects = State()
    common_stats = State()
    subject_details = State()

    # Детальная статистика
    detailed_stats = State()  # Детальная статистика по микротемам
    summary_stats = State()   # Сводка сильных/слабых тем

@router.callback_query(F.data == "progress")
async def show_progress_menu(callback: CallbackQuery, state: FSMContext):
    """Показать меню прогресса"""
    from common.premium_access import check_premium_access, show_premium_required_message, PREMIUM_FEATURES

    # Устанавливаем состояние для корректной навигации
    await state.set_state(ProgressStates.main)

    # Проверяем доступ к премиум функциям
    has_premium = await check_premium_access(callback.from_user.id)

    if not has_premium:
        feature_info = PREMIUM_FEATURES["progress"]
        await show_premium_required_message(
            callback,
            feature_info["name"],
            feature_info["description"]
        )
        return

        # Проверяем, есть ли текст в сообщении для редактирования
    try:
        await callback.message.edit_text(
            "Что именно хочешь посмотреть? Выбери один из вариантов:",
            reply_markup=get_progress_menu_kb()
        )
    except Exception as e:
        # Если не можем редактировать (например, сообщение с фото), удаляем и отправляем новое
        try:
            await callback.message.delete()
        except Exception as delete_error:
            print(f"Не удалось удалить сообщение: {delete_error}")

        await callback.message.answer(
            "Что именно хочешь посмотреть? Выбери один из вариантов:",
            reply_markup=get_progress_menu_kb()
        )

@router.callback_query(ProgressStates.main, F.data == "general_stats")
async def show_general_stats(callback: CallbackQuery, state: FSMContext):
    """Показать общую статистику"""
    from database import StudentRepository

    # Получаем студента по Telegram ID
    student = await StudentRepository.get_by_telegram_id(callback.from_user.id)

    if not student:
        # Проверяем, есть ли текст в сообщении для редактирования
        try:
            await callback.message.edit_text(
                "❌ Студент не найден в системе",
                reply_markup=get_back_to_progress_kb()
            )
        except Exception as e:
            # Если не можем редактировать (например, сообщение с фото), удаляем и отправляем новое
            try:
                await callback.message.delete()
            except Exception as delete_error:
                print(f"Не удалось удалить сообщение: {delete_error}")

            await callback.message.answer(
                "❌ Студент не найден в системе",
                reply_markup=get_back_to_progress_kb()
            )
        return

    # Получаем общую статистику студента
    general_stats = await StudentRepository.get_general_stats(student.id)

    # Проверяем, есть ли текст в сообщении для редактирования
    try:
        await callback.message.edit_text(
            f"Вот твоя краткая статистика 👇\n"
            f"📊 Баллы: {general_stats.get('total_points', 0)}\n"
            f"🎯 Уровень: {student.level}\n"
            f"📋 Выполнено ДЗ: {general_stats.get('unique_completed', 0)} (всего попыток: {general_stats.get('total_completed', 0)})",
            reply_markup=get_back_to_progress_kb()
        )
    except Exception as e:
        # Если не можем редактировать (например, сообщение с фото), удаляем и отправляем новое
        try:
            await callback.message.delete()
        except Exception as delete_error:
            print(f"Не удалось удалить сообщение: {delete_error}")

        await callback.message.answer(
            f"Вот твоя краткая статистика 👇\n"
            f"📊 Баллы: {general_stats.get('total_points', 0)}\n"
            f"🎯 Уровень: {student.level}\n"
            f"📋 Выполнено ДЗ: {general_stats.get('unique_completed', 0)} (всего попыток: {general_stats.get('total_completed', 0)})",
            reply_markup=get_back_to_progress_kb()
        )
    await state.set_state(ProgressStates.common_stats)

@router.callback_query(ProgressStates.main, F.data == "topics_understanding")
async def show_subjects_list(callback: CallbackQuery, state: FSMContext):
    """Показать список предметов для просмотра понимания по темам"""
    # Проверяем, есть ли текст в сообщении для редактирования
    try:
        await callback.message.edit_text(
            "Выбери предмет, чтобы посмотреть % понимания по микротемам:",
            reply_markup=await get_subjects_progress_kb(user_id=callback.from_user.id)
        )
    except Exception as e:
        # Если не можем редактировать (например, сообщение с фото), удаляем и отправляем новое
        try:
            await callback.message.delete()
        except Exception as delete_error:
            print(f"Не удалось удалить сообщение: {delete_error}")

        await callback.message.answer(
            "Выбери предмет, чтобы посмотреть % понимания по микротемам:",
            reply_markup=await get_subjects_progress_kb(user_id=callback.from_user.id)
        )
    await state.set_state(ProgressStates.subjects)

@router.callback_query(ProgressStates.subjects, F.data.startswith("progress_sub_"))
async def show_subject_progress(callback: CallbackQuery, state: FSMContext):
    """Показать кнопки выбора типа статистики по предмету"""
    subject_id = await check_if_id_in_callback_data("progress_sub_", callback, state, "subject")

    # Получаем ID студента из Telegram ID
    from database import StudentRepository, SubjectRepository
    student = await StudentRepository.get_by_telegram_id(callback.from_user.id)

    if not student:
        await callback.message.edit_text(
            "❌ Студент не найден в системе",
            reply_markup=get_back_to_progress_kb()
        )
        return

    # Получаем название предмета
    subject = await SubjectRepository.get_by_id(int(subject_id))
    subject_name = subject.name if subject else "Предмет"

    # Показываем кнопки выбора типа статистики
    from common.analytics.keyboards import get_student_microtopics_kb

    await callback.message.edit_text(
        f"📊 Выбери тип статистики по предмету {subject_name}:",
        reply_markup=get_student_microtopics_kb(student.id, int(subject_id))
    )
    await state.set_state(ProgressStates.subject_details)

    # Сохраняем данные для навигации
    await state.update_data(
        progress_student_id=student.id,
        progress_subject_id=int(subject_id)
    )

# Обработчики микротем регистрируются автоматически через register_microtopics_handlers

# Специальный обработчик кнопки "Назад" для состояний с картинками микротем
@router.callback_query(F.data == "back_from_microtopics_image")
async def back_from_microtopics_image(callback: CallbackQuery, state: FSMContext):
    """Возврат из состояний с картинками микротем к кнопкам выбора типа"""
    await show_subject_progress_by_saved_data(callback, state)

# Функция для возврата к кнопкам выбора статистики (для навигации)
async def show_subject_progress_by_saved_data(callback, state):
    """Показать кнопки выбора типа статистики по сохраненным данным"""
    try:
        # Получаем сохраненные данные
        data = await state.get_data()
        student_id = data.get('progress_student_id')
        subject_id = data.get('progress_subject_id')

        if not student_id or not subject_id:
            # Если данных нет, возвращаемся к списку предметов
            await show_subjects_list(callback, state)
            return

        # Получаем название предмета
        from database import SubjectRepository
        subject = await SubjectRepository.get_by_id(subject_id)
        subject_name = subject.name if subject else "Предмет"

        # Показываем кнопки выбора типа статистики
        from common.analytics.keyboards import get_student_microtopics_kb

        # Удаляем сообщение с фото и отправляем новое текстовое
        try:
            await callback.message.delete()
        except Exception as delete_error:
            print(f"Не удалось удалить сообщение: {delete_error}")

        await callback.message.answer(
            f"📊 Выбери тип статистики по предмету {subject_name}:",
            reply_markup=get_student_microtopics_kb(student_id, subject_id)
        )
        await state.set_state(ProgressStates.subject_details)

    except Exception as e:
        print(f"Ошибка при возврате к выбору статистики: {e}")
        await show_subjects_list(callback, state)


# Регистрируем универсальные обработчики микротем
register_microtopics_handlers(
    router=router,
    states_group=ProgressStates,
    role="student",
    detailed_callback_prefix="student_microtopics_page",
    summary_callback_prefix="student_summary_page",
    detailed_state=ProgressStates.detailed_stats,
    summary_state=ProgressStates.summary_stats,
    subject_details_state=ProgressStates.subject_details,
    back_keyboard_func=get_back_to_progress_kb,
    title="📌 Андрей Климов\n📈 % понимания по микротемам\n📗 Математика",
    items_per_page_detailed=15,
    items_per_page_summary=15,
    premium_check=True
)