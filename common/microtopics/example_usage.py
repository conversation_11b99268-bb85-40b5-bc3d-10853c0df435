    # Пример использования универсальных функций микротем в студенческом модуле

"""
Как адаптировать student/handlers/progress.py для использования универсальных функций:

1. Импортировать универсальные функции:
"""

from common.microtopics.handlers import (
    show_detailed_microtopics_universal,
    show_summary_microtopics_universal
)
from common.microtopics.register_handlers import register_microtopics_pagination_handlers

"""
2. Заменить старые обработчики на новые:
"""

# СТАРЫЙ КОД:
# @router.callback_query(ProgressStates.subject_details, F.data.startswith("microtopics_detailed_"))
# async def show_detailed_microtopics(callback: CallbackQuery, state: FSMContext):
#     # ... 100+ строк кода ...

# НОВЫЙ КОД:
@router.callback_query(ProgressStates.subject_details, F.data.startswith("microtopics_detailed_"))
async def show_detailed_microtopics(callback: CallbackQuery, state: FSMContext):
    """Показать детальную статистику по микротемам"""
    from common.premium_access import check_premium_access, show_premium_required_message, PREMIUM_FEATURES
    from database import StudentRepository, SubjectRepository, MicrotopicRepository
    from ..keyboards.progress import get_back_to_progress_kb

    # Проверяем доступ к премиум функциям для детальной аналитики
    has_premium = await check_premium_access(callback.from_user.id)
    if not has_premium:
        feature_info = PREMIUM_FEATURES["detailed_analytics"]
        await show_premium_required_message(
            callback,
            feature_info["name"],
            feature_info["description"]
        )
        return

    # Извлекаем student_id и subject_id из callback_data
    parts = callback.data.split("_")
    if len(parts) >= 4:
        student_id = int(parts[2])
        subject_id = int(parts[3])

        try:
            # Получаем данные студента и предмета
            student = await StudentRepository.get_by_id(student_id)
            subject = await SubjectRepository.get_by_id(subject_id)

            if not student or not subject:
                await callback.message.edit_text(
                    "❌ Студент или предмет не найден",
                    reply_markup=get_back_to_progress_kb()
                )
                return

            # Получаем данные по микротемам
            microtopic_data = await StudentRepository.get_microtopic_understanding(student_id, subject_id)
            microtopics = await MicrotopicRepository.get_by_subject(subject_id)
            microtopic_names = {mt.number: mt.name for mt in microtopics}

            # Формируем заголовок
            title = f"📌 {student.user.name}\n📈 % понимания по микротемам\n📗 {subject.name}"

            # Используем универсальную функцию
            await show_detailed_microtopics_universal(
                callback=callback,
                state=state,
                microtopic_data=microtopic_data,
                microtopic_names=microtopic_names,
                title=title,
                role="student",
                target_state=ProgressStates.detailed_stats,
                callback_prefix="student_microtopics_page",
                back_keyboard_func=get_back_to_progress_kb,
                items_per_page=30,
                caption="📊 Детальная статистика по микротемам"
            )

        except Exception as e:
            print(f"Ошибка при генерации изображения микротем: {e}")
            await callback.message.edit_text(
                "❌ Ошибка при загрузке статистики",
                reply_markup=get_back_to_progress_kb()
            )
    else:
        await callback.message.edit_text(
            "❌ Ошибка в данных запроса",
            reply_markup=get_back_to_progress_kb()
        )

"""
3. Аналогично для show_summary_microtopics:
"""

@router.callback_query(ProgressStates.subject_details, F.data.startswith("microtopics_summary_"))
async def show_summary_microtopics(callback: CallbackQuery, state: FSMContext):
    """Показать сводку по сильным и слабым темам"""
    # ... аналогичная логика получения данных ...
    
    # Используем универсальную функцию
    await show_summary_microtopics_universal(
        callback=callback,
        state=state,
        microtopic_data=microtopic_data,
        microtopic_names=microtopic_names,
        title=title,
        role="student",
        target_state=ProgressStates.summary_stats,
        callback_prefix="student_summary_page",
        back_keyboard_func=get_back_to_progress_kb,
        items_per_page=15,
        caption="📊 Сводка по сильным и слабым темам"
    )

"""
4. Регистрировать обработчики пагинации:
"""

# В конце файла student/handlers/progress.py добавить:
register_microtopics_pagination_handlers(
    router=router,
    states_group=ProgressStates,
    role="student",
    detailed_callback_prefix="student_microtopics_page",
    summary_callback_prefix="student_summary_page",
    detailed_state=ProgressStates.detailed_stats,
    summary_state=ProgressStates.summary_stats
)

"""
5. Удалить старые обработчики пагинации:
"""

# УДАЛИТЬ:
# @router.callback_query(ProgressStates.detailed_stats, F.data.startswith("student_microtopics_page_"))
# async def handle_microtopics_pagination(callback: CallbackQuery, state: FSMContext):
#     # ... старый код ...

# @router.callback_query(ProgressStates.summary_stats, F.data.startswith("student_summary_page_"))
# async def handle_summary_pagination(callback: CallbackQuery, state: FSMContext):
#     # ... старый код ...

"""
ПРЕИМУЩЕСТВА:
- Код сократился с ~400 строк до ~100 строк
- Логика пагинации стала универсальной
- Легко добавить микротемы для других ролей
- Единообразное поведение во всех модулях
"""
