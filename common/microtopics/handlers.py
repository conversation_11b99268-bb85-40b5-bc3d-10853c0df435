from aiogram.types import CallbackQuery, BufferedInputFile, InputMediaPhoto
from aiogram.fsm.context import FSMContext
from common.image_utils import generate_microtopics_table_image


async def show_detailed_microtopics_from_callback(
    callback: CallbackQuery,
    state: F<PERSON><PERSON>ontext,
    role: str,                       # "student", "curator", "teacher", etc.
    target_state,                    # Состояние для установки
    callback_prefix: str,            # "student_microtopics_page", "curator_microtopics_page"
    back_keyboard_func,              # Функция для кнопки "Назад"
    items_per_page: int = 15,        # Элементов на страницу
    caption: str = "📊 Детальная статистика по микротемам",
    premium_check: bool = True,      # Нужна ли проверка премиума
    premium_feature: str = "detailed_analytics"  # Какая премиум функция
):
    """
    Универсальная функция для отображения детальной статистики по микротемам
    Извлекает student_id и subject_id из callback_data

    Формат callback_data: microtopics_detailed_STUDENT_ID_SUBJECT_ID
    """
    # Извлекаем student_id и subject_id из callback_data
    parts = callback.data.split("_")
    if len(parts) >= 4:
        try:
            student_id = int(parts[2])
            subject_id = int(parts[3])

            await show_detailed_microtopics_universal(
                callback=callback,
                state=state,
                student_id=student_id,
                subject_id=subject_id,
                role=role,
                target_state=target_state,
                callback_prefix=callback_prefix,
                back_keyboard_func=back_keyboard_func,
                items_per_page=items_per_page,
                caption=caption,
                premium_check=premium_check,
                premium_feature=premium_feature
            )
        except ValueError:
            await callback.message.edit_text(
                "❌ Ошибка в данных запроса",
                reply_markup=back_keyboard_func()
            )
    else:
        await callback.message.edit_text(
            "❌ Ошибка в данных запроса",
            reply_markup=back_keyboard_func()
        )


async def show_summary_microtopics_from_callback(
    callback: CallbackQuery,
    state: FSMContext,
    role: str,                       # "student", "curator", "teacher", etc.
    target_state,                    # Состояние для установки
    callback_prefix: str,            # "student_summary_page", "curator_summary_page"
    back_keyboard_func,              # Функция для кнопки "Назад"
    items_per_page: int = 15,        # Элементов на страницу
    caption: str = "📊 Сводка по сильным и слабым темам",
    premium_check: bool = True,      # Нужна ли проверка премиума
    premium_feature: str = "advanced_statistics"  # Какая премиум функция
):
    """
    Универсальная функция для отображения сводки по сильным и слабым темам
    Извлекает student_id и subject_id из callback_data

    Формат callback_data: microtopics_summary_STUDENT_ID_SUBJECT_ID
    """
    # Извлекаем student_id и subject_id из callback_data
    parts = callback.data.split("_")
    if len(parts) >= 4:
        try:
            student_id = int(parts[2])
            subject_id = int(parts[3])

            await show_summary_microtopics_universal(
                callback=callback,
                state=state,
                student_id=student_id,
                subject_id=subject_id,
                role=role,
                target_state=target_state,
                callback_prefix=callback_prefix,
                back_keyboard_func=back_keyboard_func,
                items_per_page=items_per_page,
                caption=caption,
                premium_check=premium_check,
                premium_feature=premium_feature
            )
        except ValueError:
            await callback.message.edit_text(
                "❌ Ошибка в данных запроса",
                reply_markup=back_keyboard_func()
            )
    else:
        await callback.message.edit_text(
            "❌ Ошибка в данных запроса",
            reply_markup=back_keyboard_func()
        )


async def show_detailed_microtopics_universal(
    callback: CallbackQuery,
    state: FSMContext,
    student_id: int,                 # ID студента
    subject_id: int,                 # ID предмета
    role: str,                       # "student", "curator", "teacher", etc.
    target_state,                    # Состояние для установки
    callback_prefix: str,            # "student_microtopics_page", "curator_microtopics_page"
    back_keyboard_func,              # Функция для кнопки "Назад"
    items_per_page: int = 15,        # Элементов на страницу
    caption: str = "📊 Детальная статистика по микротемам",
    premium_check: bool = True,      # Нужна ли проверка премиума
    premium_feature: str = "detailed_analytics"  # Какая премиум функция
):
    """
    Универсальная функция для отображения детальной статистики по микротемам

    Args:
        callback: Объект CallbackQuery
        state: Контекст состояния FSM
        student_id: ID студента
        subject_id: ID предмета
        role: Роль пользователя
        target_state: Состояние для установки
        callback_prefix: Префикс для callback пагинации
        back_keyboard_func: Функция для генерации кнопки "Назад"
        items_per_page: Количество элементов на страницу
        caption: Подпись к изображению
        premium_check: Нужна ли проверка премиума
        premium_feature: Какая премиум функция
    """
    try:
        # Проверяем доступ к премиум функциям
        if premium_check:
            from common.premium_access import check_premium_access, show_premium_required_message, PREMIUM_FEATURES

            has_premium = await check_premium_access(callback.from_user.id)
            if not has_premium:
                feature_info = PREMIUM_FEATURES[premium_feature]
                await show_premium_required_message(
                    callback,
                    feature_info["name"],
                    feature_info["description"]
                )
                return

        # Получаем данные студента и предмета
        from database import StudentRepository, SubjectRepository, MicrotopicRepository

        student = await StudentRepository.get_by_id(student_id)
        subject = await SubjectRepository.get_by_id(subject_id)

        if not student or not subject:
            await callback.message.edit_text(
                "❌ Студент или предмет не найден",
                reply_markup=back_keyboard_func()
            )
            return

        # Получаем данные по микротемам
        microtopic_data = await StudentRepository.get_microtopic_understanding(student_id, subject_id)
        microtopics = await MicrotopicRepository.get_by_subject(subject_id)
        microtopic_names = {mt.number: mt.name for mt in microtopics}

        if not microtopic_data:
            await callback.message.edit_text(
                f"📌 {student.user.name}\n❌ Пока не выполнено ни одного задания по микротемам предмета {subject.name}",
                reply_markup=back_keyboard_func()
            )
            return

        # Формируем заголовок
        title = f"📌 {student.user.name}\n📈 % понимания по микротемам\n📗 {subject.name}"

        # Генерируем изображение таблицы (первая страница)
        page = 0
        total_items = len(microtopic_data)

        image_bytes = await generate_microtopics_table_image(
            microtopic_data=microtopic_data,
            microtopic_names=microtopic_names,
            title=title,
            display_mode="detailed",
            data_source=role,
            page=page,
            items_per_page=items_per_page
        )

        # Отправляем изображение
        photo = BufferedInputFile(image_bytes, filename="microtopics_detailed.png")
        await callback.message.delete()

        from .keyboards import get_microtopics_pagination_kb
        await callback.message.answer_photo(
            photo=photo,
            caption=caption,
            reply_markup=get_microtopics_pagination_kb(
                current_page=page,
                total_items=total_items,
                items_per_page=items_per_page,
                callback_prefix=callback_prefix,
                back_keyboard_func=None
            )
        )

        # Устанавливаем состояние детальной статистики
        await state.set_state(target_state)

        # Сохраняем данные для навигации и пагинации
        await state.update_data(
            microtopic_data=microtopic_data,
            microtopic_names=microtopic_names,
            display_title=title,
            current_page=page,
            items_per_page=items_per_page,
            total_items=total_items,
            display_mode="detailed",
            role=role,
            callback_prefix=callback_prefix,
            caption=caption
        )

    except Exception as e:
        print(f"Ошибка при генерации изображения микротем: {e}")
        await callback.message.edit_text(
            "❌ Ошибка при загрузке статистики",
            reply_markup=back_keyboard_func()
        )


async def show_summary_microtopics_universal(
    callback: CallbackQuery,
    state: FSMContext,
    student_id: int,                 # ID студента
    subject_id: int,                 # ID предмета
    role: str,                       # "student", "curator", "teacher", etc.
    target_state,                    # Состояние для установки
    callback_prefix: str,            # "student_summary_page", "curator_summary_page"
    back_keyboard_func,              # Функция для кнопки "Назад"
    items_per_page: int = 15,        # Элементов на страницу
    caption: str = "📊 Сводка по сильным и слабым темам",
    premium_check: bool = True,      # Нужна ли проверка премиума
    premium_feature: str = "advanced_statistics"  # Какая премиум функция
):
    """
    Универсальная функция для отображения сводки по сильным и слабым темам

    Args:
        callback: Объект CallbackQuery
        state: Контекст состояния FSM
        student_id: ID студента
        subject_id: ID предмета
        role: Роль пользователя
        target_state: Состояние для установки
        callback_prefix: Префикс для callback пагинации
        back_keyboard_func: Функция для генерации кнопки "Назад"
        items_per_page: Количество элементов на страницу
        caption: Подпись к изображению
        premium_check: Нужна ли проверка премиума
        premium_feature: Какая премиум функция
    """
    try:
        # Проверяем доступ к премиум функциям
        if premium_check:
            from common.premium_access import check_premium_access, show_premium_required_message, PREMIUM_FEATURES

            has_premium = await check_premium_access(callback.from_user.id)
            if not has_premium:
                feature_info = PREMIUM_FEATURES[premium_feature]
                await show_premium_required_message(
                    callback,
                    feature_info["name"],
                    feature_info["description"]
                )
                return

        # Получаем данные студента и предмета
        from database import StudentRepository, SubjectRepository, MicrotopicRepository

        student = await StudentRepository.get_by_id(student_id)
        subject = await SubjectRepository.get_by_id(subject_id)

        if not student or not subject:
            await callback.message.edit_text(
                "❌ Студент или предмет не найден",
                reply_markup=back_keyboard_func()
            )
            return

        # Получаем данные по микротемам
        microtopic_data = await StudentRepository.get_microtopic_understanding(student_id, subject_id)
        microtopics = await MicrotopicRepository.get_by_subject(subject_id)
        microtopic_names = {mt.number: mt.name for mt in microtopics}

        if not microtopic_data:
            await callback.message.edit_text(
                f"📌 {student.user.name}\n❌ Пока не выполнено ни одного задания для анализа сильных и слабых тем по предмету {subject.name}",
                reply_markup=back_keyboard_func()
            )
            return

        # Формируем заголовок
        title = f"📌 {student.user.name}\n🔍 Сильные и слабые темы\n📗 {subject.name}"

        # Генерируем изображение таблицы (режим сводки с пагинацией)
        page = 0
        total_items = len(microtopic_data)

        image_bytes = await generate_microtopics_table_image(
            microtopic_data=microtopic_data,
            microtopic_names=microtopic_names,
            title=title,
            display_mode="summary",
            data_source=role,
            page=page,
            items_per_page=items_per_page
        )

        # Отправляем изображение
        photo = BufferedInputFile(image_bytes, filename="microtopics_summary.png")
        await callback.message.delete()

        from .keyboards import get_microtopics_pagination_kb
        await callback.message.answer_photo(
            photo=photo,
            caption=caption,
            reply_markup=get_microtopics_pagination_kb(
                current_page=page,
                total_items=total_items,
                items_per_page=items_per_page,
                callback_prefix=callback_prefix,
                back_keyboard_func=None
            )
        )

        # Устанавливаем состояние сводки
        await state.set_state(target_state)

        # Сохраняем данные для навигации и пагинации
        await state.update_data(
            microtopic_data=microtopic_data,
            microtopic_names=microtopic_names,
            display_title=title,
            current_page=page,
            items_per_page=items_per_page,
            total_items=total_items,
            display_mode="summary",
            role=role,
            callback_prefix=callback_prefix,
            caption=caption
        )

    except Exception as e:
        print(f"Ошибка при генерации изображения сводки микротем: {e}")
        await callback.message.edit_text(
            "❌ Ошибка при загрузке сводки",
            reply_markup=back_keyboard_func()
        )


async def handle_microtopics_pagination_universal(
    callback: CallbackQuery,
    state: FSMContext,
    callback_prefix: str,            # "student_microtopics_page"
    display_mode: str,               # "detailed" или "summary"
    role: str                        # "student", "curator", etc.
):
    """
    Универсальная функция для обработки пагинации микротем
    
    Args:
        callback: Объект CallbackQuery
        state: Контекст состояния FSM
        callback_prefix: Префикс callback для извлечения номера страницы
        display_mode: Режим отображения ("detailed" или "summary")
        role: Роль пользователя
    """
    try:
        # Извлекаем номер страницы из callback_data
        page_str = callback.data.split(f"{callback_prefix}_")[1]
        new_page = int(page_str)

        # Получаем сохраненные данные
        data = await state.get_data()
        microtopic_data = data.get('microtopic_data')
        microtopic_names = data.get('microtopic_names')
        title = data.get('display_title')
        items_per_page = data.get('items_per_page', 15)
        total_items = data.get('total_items')
        caption = data.get('caption', "📊 Статистика по микротемам")

        if not microtopic_data or not microtopic_names:
            await callback.answer("❌ Ошибка: данные не найдены")
            return

        # Генерируем изображение для новой страницы
        image_bytes = await generate_microtopics_table_image(
            microtopic_data=microtopic_data,
            microtopic_names=microtopic_names,
            title=title,
            display_mode=display_mode,
            data_source=role,
            page=new_page,
            items_per_page=items_per_page
        )

        # Обновляем изображение
        photo = BufferedInputFile(image_bytes, filename=f"microtopics_{display_mode}_page_{new_page}.png")

        from .keyboards import get_microtopics_pagination_kb
        await callback.message.edit_media(
            media=InputMediaPhoto(media=photo),
            reply_markup=get_microtopics_pagination_kb(
                current_page=new_page,
                total_items=total_items,
                items_per_page=items_per_page,
                callback_prefix=callback_prefix,
                back_keyboard_func=None
            )
        )

        # Обновляем текущую страницу в состоянии
        await state.update_data(current_page=new_page)

        await callback.answer()

    except Exception as e:
        print(f"Ошибка при обработке пагинации микротем: {e}")
        await callback.answer("❌ Ошибка при переключении страницы")
